/* 精密装配自动化硬件系统方案 - HTML样式表 */

/* 基础样式 */
body {
    font-family: "Microsoft YaHei", "SimSun", Arial, sans-serif;
    line-height: 1.6;
    color: #333;
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    background-color: #fafafa;
}

/* 标题样式 */
h1 {
    color: #2c3e50;
    border-bottom: 3px solid #3498db;
    padding-bottom: 10px;
    margin-bottom: 30px;
    font-size: 2.5em;
    text-align: center;
}

h2 {
    color: #34495e;
    border-bottom: 2px solid #e74c3c;
    padding-bottom: 8px;
    margin-top: 40px;
    margin-bottom: 20px;
    font-size: 1.8em;
}

h3 {
    color: #2980b9;
    border-left: 4px solid #3498db;
    padding-left: 15px;
    margin-top: 30px;
    margin-bottom: 15px;
    font-size: 1.4em;
}

h4 {
    color: #27ae60;
    margin-top: 25px;
    margin-bottom: 12px;
    font-size: 1.2em;
}

/* 目录样式 */
#TOC {
    background-color: #ecf0f1;
    border: 1px solid #bdc3c7;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 30px;
}

#TOC ul {
    list-style-type: none;
    padding-left: 0;
}

#TOC li {
    margin: 8px 0;
}

#TOC a {
    color: #2980b9;
    text-decoration: none;
    padding: 5px 10px;
    border-radius: 4px;
    transition: background-color 0.3s;
}

#TOC a:hover {
    background-color: #3498db;
    color: white;
}

/* 段落和文本样式 */
p {
    margin-bottom: 15px;
    text-align: justify;
}

/* 列表样式 */
ul, ol {
    margin-bottom: 15px;
    padding-left: 30px;
}

li {
    margin-bottom: 8px;
}

/* 表格样式 */
table {
    width: 100%;
    border-collapse: collapse;
    margin: 20px 0;
    background-color: white;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

th, td {
    border: 1px solid #ddd;
    padding: 12px;
    text-align: left;
}

th {
    background-color: #3498db;
    color: white;
    font-weight: bold;
}

tr:nth-child(even) {
    background-color: #f8f9fa;
}

tr:hover {
    background-color: #e8f4fd;
}

/* 代码块样式 */
pre {
    background-color: #2c3e50;
    color: #ecf0f1;
    padding: 20px;
    border-radius: 8px;
    overflow-x: auto;
    margin: 20px 0;
    border-left: 4px solid #e74c3c;
}

code {
    background-color: #ecf0f1;
    color: #e74c3c;
    padding: 2px 6px;
    border-radius: 4px;
    font-family: "Consolas", "Monaco", monospace;
}

/* Mermaid图表容器样式 */
.mermaid {
    text-align: center;
    margin: 30px 0;
    padding: 20px;
    background-color: white;
    border: 1px solid #ddd;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* 强调文本样式 */
strong {
    color: #e74c3c;
    font-weight: bold;
}

em {
    color: #8e44ad;
    font-style: italic;
}

/* 引用块样式 */
blockquote {
    border-left: 4px solid #f39c12;
    background-color: #fef9e7;
    padding: 15px 20px;
    margin: 20px 0;
    font-style: italic;
}

/* 链接样式 */
a {
    color: #3498db;
    text-decoration: none;
}

a:hover {
    color: #2980b9;
    text-decoration: underline;
}

/* 图片样式 */
img {
    max-width: 100%;
    height: auto;
    display: block;
    margin: 20px auto;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

/* 分隔线样式 */
hr {
    border: none;
    height: 2px;
    background: linear-gradient(to right, #3498db, #e74c3c, #f39c12);
    margin: 40px 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
    body {
        padding: 10px;
        font-size: 14px;
    }
    
    h1 {
        font-size: 2em;
    }
    
    h2 {
        font-size: 1.5em;
    }
    
    h3 {
        font-size: 1.3em;
    }
    
    table {
        font-size: 12px;
    }
    
    pre {
        padding: 10px;
        font-size: 12px;
    }
}

/* 打印样式 */
@media print {
    body {
        background-color: white;
        color: black;
        font-size: 12pt;
    }
    
    #TOC {
        page-break-after: always;
    }
    
    h1, h2, h3 {
        page-break-after: avoid;
    }
    
    table {
        page-break-inside: avoid;
    }
    
    pre {
        page-break-inside: avoid;
    }
}

/* 特殊内容样式 */
.technical-spec {
    background-color: #e8f6f3;
    border: 1px solid #16a085;
    border-radius: 6px;
    padding: 15px;
    margin: 15px 0;
}

.warning {
    background-color: #fdf2e9;
    border: 1px solid #e67e22;
    border-radius: 6px;
    padding: 15px;
    margin: 15px 0;
}

.note {
    background-color: #eaf2f8;
    border: 1px solid #2980b9;
    border-radius: 6px;
    padding: 15px;
    margin: 15px 0;
}

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>精密装配自动化硬件系统方案</title>
    <link rel="stylesheet" href="style.css">
    <!-- Mermaid支持 -->
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <script>
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true,
                curve: 'basis'
            },
            themeVariables: {
                primaryColor: '#3498db',
                primaryTextColor: '#2c3e50',
                primaryBorderColor: '#2980b9',
                lineColor: '#34495e',
                secondaryColor: '#ecf0f1',
                tertiaryColor: '#f8f9fa'
            }
        });
    </script>
    <style>
        /* 增强的Mermaid图表样式 */
        .mermaid-container {
            background-color: white;
            border: 2px solid #e1e8ed;
            border-radius: 12px;
            padding: 30px;
            margin: 30px 0;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .mermaid-title {
            font-size: 1.3em;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 20px;
            text-align: center;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        
        .mermaid {
            max-width: 100%;
            overflow-x: auto;
        }
        
        /* 图表说明样式 */
        .chart-description {
            background-color: #f8f9fa;
            border-left: 4px solid #3498db;
            padding: 20px;
            margin: 20px 0;
            border-radius: 0 8px 8px 0;
        }
        
        .chart-description h4 {
            color: #2c3e50;
            margin-top: 0;
            margin-bottom: 15px;
        }
        
        .chart-description ul {
            margin-bottom: 10px;
        }
        
        .chart-description li {
            margin-bottom: 8px;
        }
        
        /* 技术规格框 */
        .tech-specs {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        
        .tech-specs h3 {
            color: white;
            border: none;
            margin-top: 0;
        }
        
        /* 警告框 */
        .warning-box {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-left: 4px solid #fdcb6e;
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
        }
        
        .warning-box::before {
            content: "⚠️ ";
            font-weight: bold;
        }
        
        /* 成功框 */
        .success-box {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            border-left: 4px solid #28a745;
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
        }
        
        .success-box::before {
            content: "✅ ";
            font-weight: bold;
        }
        
        /* 信息框 */
        .info-box {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            border-left: 4px solid #17a2b8;
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
        }
        
        .info-box::before {
            content: "ℹ️ ";
            font-weight: bold;
        }
        
        /* 页面顶部导航 */
        .page-nav {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: rgba(52, 73, 94, 0.9);
            color: white;
            padding: 10px 15px;
            border-radius: 25px;
            font-size: 14px;
            z-index: 1000;
            box-shadow: 0 2px 10px rgba(0,0,0,0.2);
        }
        
        .page-nav a {
            color: white;
            text-decoration: none;
            margin: 0 5px;
        }
        
        .page-nav a:hover {
            text-decoration: underline;
        }
        
        /* 返回顶部按钮 */
        .back-to-top {
            position: fixed;
            bottom: 30px;
            right: 30px;
            background-color: #3498db;
            color: white;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            border: none;
            cursor: pointer;
            font-size: 18px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.2);
            transition: all 0.3s ease;
            z-index: 1000;
        }
        
        .back-to-top:hover {
            background-color: #2980b9;
            transform: translateY(-2px);
        }
        
        /* 打印优化 */
        @media print {
            .page-nav,
            .back-to-top {
                display: none;
            }
            
            .mermaid-container {
                page-break-inside: avoid;
            }
        }
    </style>
</head>
<body>
    <!-- 页面导航 -->
    <div class="page-nav">
        <a href="#TOC">目录</a> |
        <a href="#系统架构与布局">架构</a> |
        <a href="#硬件系统配置">硬件</a> |
        <a href="#装配工艺流程">工艺</a>
    </div>
    
    <!-- 返回顶部按钮 -->
    <button class="back-to-top" onclick="window.scrollTo({top: 0, behavior: 'smooth'})" title="返回顶部">
        ↑
    </button>
    
    <div id="header">
        <h1 class="title">精密装配自动化硬件系统方案</h1>
        <div class="info-box">
            <strong>文档说明：</strong>本文档详细描述了精密装配自动化硬件系统的完整技术方案，包括系统架构、硬件配置、工艺流程等核心内容。
        </div>
    </div>
    
    <!-- 这里将插入转换后的内容 -->
    
    <script>
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 为所有的Mermaid代码块添加容器
            const mermaidBlocks = document.querySelectorAll('pre code.language-mermaid, .mermaid');
            mermaidBlocks.forEach(function(block, index) {
                const container = document.createElement('div');
                container.className = 'mermaid-container';
                
                const title = document.createElement('div');
                title.className = 'mermaid-title';
                title.textContent = '流程图 ' + (index + 1);
                
                container.appendChild(title);
                block.parentNode.insertBefore(container, block);
                container.appendChild(block);
            });
            
            // 平滑滚动
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });
        });
    </script>
</body>
</html>
